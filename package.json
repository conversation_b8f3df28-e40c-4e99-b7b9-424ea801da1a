{"name": "chrome-plugin-one-tab", "version": "1.7.64", "description": "一个高效的Chrome标签页管理扩展", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:dev": "tsc && vite build", "package": "pnpm build && node package-extension.js", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "type-check": "tsc --noEmit", "validate": "node validate-extension.js"}, "keywords": ["chrome-extension", "tab-management", "react", "typescript"], "author": "", "license": "MIT", "packageManager": "pnpm@10.15.0", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.2.1", "@supabase/supabase-js": "^2.49.4", "@types/lodash": "^4.17.16", "@types/react-window": "^1.8.8", "lodash": "^4.17.21", "lz-string": "^1.5.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-window": "^1.8.11"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.21", "@types/chrome": "^0.0.262", "@types/lz-string": "^1.5.0", "@types/node": "^22.14.0", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "tailwindcss": "^3.4.1", "typescript": "^5.4.2", "vite": "^4.5.2"}}